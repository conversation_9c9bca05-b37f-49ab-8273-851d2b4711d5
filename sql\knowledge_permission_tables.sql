-- 知识库权限管理相关表结构

-- 创建知识库权限表
DROP TABLE IF EXISTS `knowledge_base_permission`;
CREATE TABLE `knowledge_base_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `knowledge_base_id` bigint NOT NULL COMMENT '知识库ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户名称',
  `permission_type` varchar(20) NOT NULL DEFAULT 'read' COMMENT '权限类型（read:只读, write:读写, admin:管理员）',
  `granted_by` bigint NOT NULL COMMENT '授权人ID',
  `granted_by_name` varchar(30) NOT NULL COMMENT '授权人名称',
  `grant_time` datetime NOT NULL COMMENT '授权时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_kb_user` (`knowledge_base_id`, `user_id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_granted_by` (`granted_by`),
  KEY `idx_permission_type` (`permission_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库权限表';

-- 插入知识库管理相关菜单
-- 获取系统管理菜单ID
SET @system_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '系统管理' AND parent_id = 0);

-- 插入知识库管理父菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('知识库管理', @system_menu_id, 8, 'knowledge', NULL, 1, 0, 'M', '0', '0', NULL, 'education', 'admin', sysdate(), '', NULL, '知识库管理目录');

-- 获取知识库管理父菜单ID
SET @knowledge_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '知识库管理' AND parent_id = @system_menu_id);

-- 插入知识库权限管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('权限管理', @knowledge_menu_id, 1, 'permission', 'knowledge/permission/index', 1, 0, 'C', '0', '0', 'knowledge:permission:list', 'peoples', 'admin', sysdate(), '', NULL, '知识库权限管理页面');

-- 获取权限管理菜单ID
SET @permission_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '权限管理' AND parent_id = @knowledge_menu_id);

-- 插入权限管理相关按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('权限查询', @permission_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:query', '#', 'admin', sysdate(), '', NULL, ''),
('权限新增', @permission_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:add', '#', 'admin', sysdate(), '', NULL, ''),
('权限修改', @permission_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:edit', '#', 'admin', sysdate(), '', NULL, ''),
('权限删除', @permission_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:remove', '#', 'admin', sysdate(), '', NULL, ''),
('权限导出', @permission_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:export', '#', 'admin', sysdate(), '', NULL, '');

-- 为管理员角色分配知识库管理相关权限（避免重复插入）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu
WHERE (perms LIKE 'knowledge:permission:%' OR (parent_id IN (SELECT menu_id FROM sys_menu WHERE path = 'knowledge')))
AND menu_id NOT IN (SELECT menu_id FROM sys_role_menu WHERE role_id = 1);

COMMIT;
